# -*- coding: utf-8 -*-
"""
回测引擎模块
提供策略回测和性能评估功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False

class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = 1000000, 
                 commission_rate: float = 0.0003,
                 slippage_rate: float = 0.0001):
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        
        # 回测结果
        self.portfolio_value = pd.DataFrame()
        self.positions = pd.DataFrame()
        self.trades = pd.DataFrame()
        self.performance_metrics = {}
        
    def run_backtest(self, weights: pd.DataFrame, 
                    price_data: pd.DataFrame,
                    start_date: str = None,
                    end_date: str = None) -> Dict:
        """
        运行回测
        
        Parameters:
        -----------
        weights : pd.DataFrame
            策略权重数据，包含columns: ['datetime', 'code', 'weight']
        price_data : pd.DataFrame  
            价格数据，包含columns: ['datetime', 'code', 'close', 'pct_chg']
        start_date : str
            回测开始日期
        end_date : str
            回测结束日期
        """
        
        # 数据预处理
        if start_date:
            weights = weights[weights['datetime'] >= start_date]
            price_data = price_data[price_data['datetime'] >= start_date]
        if end_date:
            weights = weights[weights['datetime'] <= end_date]
            price_data = price_data[price_data['datetime'] <= end_date]
        
        # 获取交易日期
        trade_dates = sorted(weights['datetime'].unique())
        
        # 初始化
        current_capital = self.initial_capital
        current_positions = {}
        portfolio_values = []
        position_records = []
        trade_records = []
        
        for i, date in enumerate(trade_dates):
            # 获取当日权重和价格
            date_weights = weights[weights['datetime'] == date]
            date_prices = price_data[price_data['datetime'] == date]
            
            if len(date_prices) == 0:
                continue
                
            # 合并权重和价格数据
            merged_data = pd.merge(date_weights, date_prices, 
                                 on=['datetime', 'code'], how='inner')
            
            if i == 0:
                # 首日建仓
                for _, row in merged_data.iterrows():
                    if abs(row['weight']) > 0.001:  # 权重阈值
                        target_value = current_capital * row['weight']
                        shares = int(target_value / row['close'])
                        
                        if shares != 0:
                            # 计算交易成本
                            trade_value = abs(shares * row['close'])
                            commission = trade_value * self.commission_rate
                            slippage = trade_value * self.slippage_rate
                            total_cost = commission + slippage
                            
                            current_positions[row['code']] = {
                                'shares': shares,
                                'avg_price': row['close'],
                                'market_value': shares * row['close']
                            }
                            
                            current_capital -= (shares * row['close'] + total_cost)
                            
                            # 记录交易
                            trade_records.append({
                                'datetime': date,
                                'code': row['code'],
                                'action': 'BUY' if shares > 0 else 'SELL',
                                'shares': abs(shares),
                                'price': row['close'],
                                'commission': commission,
                                'slippage': slippage
                            })
            else:
                # 调仓
                # 计算当前持仓市值
                total_market_value = current_capital
                for code, position in current_positions.items():
                    current_price_data = date_prices[date_prices['code'] == code]
                    if len(current_price_data) > 0:
                        current_price = current_price_data.iloc[0]['close']
                        position['market_value'] = position['shares'] * current_price
                        total_market_value += position['market_value']
                
                # 计算目标持仓
                target_positions = {}
                for _, row in merged_data.iterrows():
                    if abs(row['weight']) > 0.001:
                        target_value = total_market_value * row['weight']
                        target_shares = int(target_value / row['close'])
                        target_positions[row['code']] = target_shares
                
                # 执行调仓
                all_codes = set(list(current_positions.keys()) + list(target_positions.keys()))
                
                for code in all_codes:
                    current_shares = current_positions.get(code, {}).get('shares', 0)
                    target_shares = target_positions.get(code, 0)
                    
                    if current_shares != target_shares:
                        trade_shares = target_shares - current_shares
                        
                        # 获取当前价格
                        code_price_data = date_prices[date_prices['code'] == code]
                        if len(code_price_data) == 0:
                            continue
                        current_price = code_price_data.iloc[0]['close']
                        
                        # 计算交易成本
                        trade_value = abs(trade_shares * current_price)
                        commission = trade_value * self.commission_rate
                        slippage = trade_value * self.slippage_rate
                        total_cost = commission + slippage
                        
                        # 更新持仓
                        if target_shares == 0:
                            # 清仓
                            if code in current_positions:
                                del current_positions[code]
                        else:
                            # 更新持仓
                            current_positions[code] = {
                                'shares': target_shares,
                                'avg_price': current_price,
                                'market_value': target_shares * current_price
                            }
                        
                        # 更新现金
                        current_capital -= (trade_shares * current_price + total_cost)
                        
                        # 记录交易
                        if abs(trade_shares) > 0:
                            trade_records.append({
                                'datetime': date,
                                'code': code,
                                'action': 'BUY' if trade_shares > 0 else 'SELL',
                                'shares': abs(trade_shares),
                                'price': current_price,
                                'commission': commission,
                                'slippage': slippage
                            })
            
            # 计算当日组合价值
            total_value = current_capital
            for code, position in current_positions.items():
                code_price_data = date_prices[date_prices['code'] == code]
                if len(code_price_data) > 0:
                    current_price = code_price_data.iloc[0]['close']
                    position['market_value'] = position['shares'] * current_price
                    total_value += position['market_value']
            
            portfolio_values.append({
                'datetime': date,
                'total_value': total_value,
                'cash': current_capital,
                'market_value': total_value - current_capital
            })
            
            # 记录持仓
            for code, position in current_positions.items():
                position_records.append({
                    'datetime': date,
                    'code': code,
                    'shares': position['shares'],
                    'market_value': position['market_value']
                })
        
        # 保存结果
        self.portfolio_value = pd.DataFrame(portfolio_values)
        self.positions = pd.DataFrame(position_records)
        self.trades = pd.DataFrame(trade_records)
        
        # 计算性能指标
        self._calculate_performance_metrics()
        
        return {
            'portfolio_value': self.portfolio_value,
            'positions': self.positions,
            'trades': self.trades,
            'performance_metrics': self.performance_metrics
        }
    
    def _calculate_performance_metrics(self):
        """计算性能指标"""
        if len(self.portfolio_value) == 0:
            return
        
        # 计算收益率
        self.portfolio_value['returns'] = self.portfolio_value['total_value'].pct_change()
        
        # 基本指标
        total_return = (self.portfolio_value['total_value'].iloc[-1] / 
                       self.portfolio_value['total_value'].iloc[0] - 1)
        
        annual_return = (1 + total_return) ** (252 / len(self.portfolio_value)) - 1
        
        volatility = self.portfolio_value['returns'].std() * np.sqrt(252)
        
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # 最大回撤
        cumulative = (1 + self.portfolio_value['returns'].fillna(0)).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()
        
        self.performance_metrics = {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': len(self.trades)
        }
    
    def plot_performance(self, benchmark_data: pd.DataFrame = None):
        """绘制性能图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 净值曲线
        axes[0, 0].plot(pd.to_datetime(self.portfolio_value['datetime']), 
                       self.portfolio_value['total_value'] / self.initial_capital)
        if benchmark_data is not None:
            axes[0, 0].plot(pd.to_datetime(benchmark_data['datetime']), 
                           benchmark_data['cumulative_return'], label='基准')
        axes[0, 0].set_title('净值曲线')
        axes[0, 0].set_ylabel('净值')
        axes[0, 0].legend()
        
        # 回撤
        cumulative = (1 + self.portfolio_value['returns'].fillna(0)).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        axes[0, 1].fill_between(pd.to_datetime(self.portfolio_value['datetime']), 
                               drawdown, 0, alpha=0.3, color='red')
        axes[0, 1].set_title('回撤')
        axes[0, 1].set_ylabel('回撤比例')
        
        # 收益分布
        axes[1, 0].hist(self.portfolio_value['returns'].dropna(), bins=50, alpha=0.7)
        axes[1, 0].set_title('收益率分布')
        axes[1, 0].set_xlabel('日收益率')
        
        # 滚动夏普比率
        rolling_sharpe = (self.portfolio_value['returns'].rolling(60).mean() / 
                         self.portfolio_value['returns'].rolling(60).std() * np.sqrt(252))
        axes[1, 1].plot(pd.to_datetime(self.portfolio_value['datetime']), rolling_sharpe)
        axes[1, 1].set_title('滚动夏普比率(60日)')
        axes[1, 1].set_ylabel('夏普比率')
        
        plt.tight_layout()
        plt.show()
    
    def get_performance_summary(self) -> pd.DataFrame:
        """获取性能摘要"""
        summary = pd.DataFrame([self.performance_metrics]).T
        summary.columns = ['值']
        summary.index.name = '指标'
        return summary

# 使用示例
def example_backtest():
    """回测示例"""
    # 创建回测引擎
    engine = BacktestEngine(initial_capital=1000000)
    
    print("回测引擎初始化完成")
    print(f"初始资金: {engine.initial_capital:,.0f}")
    print(f"手续费率: {engine.commission_rate:.4f}")
    print(f"滑点率: {engine.slippage_rate:.4f}")
    
    return engine

if __name__ == "__main__":
    engine = example_backtest()
