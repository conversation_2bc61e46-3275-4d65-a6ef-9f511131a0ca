# -*- coding: utf-8 -*-
"""
交易执行引擎
提供订单管理和交易执行功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
from enum import Enum
import time
import warnings
warnings.filterwarnings('ignore')

class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    PARTIAL_FILLED = "partial_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"

class Order:
    """订单类"""
    
    def __init__(self, order_id: str, symbol: str, side: OrderSide,
                 order_type: OrderType, quantity: int, price: float = None,
                 stop_price: float = None):
        self.order_id = order_id
        self.symbol = symbol
        self.side = side
        self.order_type = order_type
        self.quantity = quantity
        self.price = price
        self.stop_price = stop_price
        
        self.status = OrderStatus.PENDING
        self.filled_quantity = 0
        self.avg_fill_price = 0.0
        self.create_time = datetime.now()
        self.update_time = datetime.now()
        self.fills = []
        
    def add_fill(self, quantity: int, price: float, timestamp: datetime = None):
        """添加成交记录"""
        if timestamp is None:
            timestamp = datetime.now()
            
        fill = {
            'quantity': quantity,
            'price': price,
            'timestamp': timestamp
        }
        self.fills.append(fill)
        
        # 更新订单状态
        self.filled_quantity += quantity
        
        # 计算平均成交价
        total_value = sum(f['quantity'] * f['price'] for f in self.fills)
        self.avg_fill_price = total_value / self.filled_quantity
        
        # 更新订单状态
        if self.filled_quantity >= self.quantity:
            self.status = OrderStatus.FILLED
        else:
            self.status = OrderStatus.PARTIAL_FILLED
            
        self.update_time = timestamp
    
    def cancel(self):
        """取消订单"""
        if self.status in [OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED]:
            self.status = OrderStatus.CANCELLED
            self.update_time = datetime.now()
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'status': self.status.value,
            'filled_quantity': self.filled_quantity,
            'avg_fill_price': self.avg_fill_price,
            'create_time': self.create_time,
            'update_time': self.update_time
        }

class MockBroker:
    """模拟券商接口"""
    
    def __init__(self, commission_rate: float = 0.0003, 
                 slippage_rate: float = 0.0001):
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self.market_data = {}  # {symbol: {'price': float, 'timestamp': datetime}}
        
    def update_market_data(self, symbol: str, price: float):
        """更新市场数据"""
        self.market_data[symbol] = {
            'price': price,
            'timestamp': datetime.now()
        }
    
    def submit_order(self, order: Order) -> bool:
        """提交订单"""
        # 模拟订单提交
        if order.symbol not in self.market_data:
            order.status = OrderStatus.REJECTED
            return False
        
        # 市价单立即成交
        if order.order_type == OrderType.MARKET:
            market_price = self.market_data[order.symbol]['price']
            
            # 添加滑点
            if order.side == OrderSide.BUY:
                fill_price = market_price * (1 + self.slippage_rate)
            else:
                fill_price = market_price * (1 - self.slippage_rate)
            
            order.add_fill(order.quantity, fill_price)
            return True
        
        # 限价单需要检查价格
        elif order.order_type == OrderType.LIMIT:
            market_price = self.market_data[order.symbol]['price']
            
            # 简单的限价单逻辑
            if ((order.side == OrderSide.BUY and market_price <= order.price) or
                (order.side == OrderSide.SELL and market_price >= order.price)):
                order.add_fill(order.quantity, order.price)
                return True
        
        return False
    
    def cancel_order(self, order: Order) -> bool:
        """取消订单"""
        order.cancel()
        return True

class TradingEngine:
    """交易执行引擎"""
    
    def __init__(self, broker: MockBroker = None):
        self.broker = broker or MockBroker()
        self.orders = {}  # {order_id: Order}
        self.positions = {}  # {symbol: {'quantity': int, 'avg_price': float}}
        self.cash = 1000000.0  # 初始现金
        self.order_counter = 0
        
        # 交易记录
        self.trade_history = []
        self.position_history = []
        
    def generate_order_id(self) -> str:
        """生成订单ID"""
        self.order_counter += 1
        return f"ORDER_{datetime.now().strftime('%Y%m%d')}_{self.order_counter:06d}"
    
    def submit_order(self, symbol: str, side: OrderSide, quantity: int,
                    order_type: OrderType = OrderType.MARKET,
                    price: float = None, stop_price: float = None) -> str:
        """提交订单"""
        order_id = self.generate_order_id()
        
        order = Order(
            order_id=order_id,
            symbol=symbol,
            side=side,
            order_type=order_type,
            quantity=quantity,
            price=price,
            stop_price=stop_price
        )
        
        # 检查资金和持仓
        if not self._validate_order(order):
            order.status = OrderStatus.REJECTED
            self.orders[order_id] = order
            return order_id
        
        # 提交到券商
        success = self.broker.submit_order(order)
        
        if success and order.status == OrderStatus.FILLED:
            self._process_fill(order)
        
        self.orders[order_id] = order
        return order_id
    
    def _validate_order(self, order: Order) -> bool:
        """验证订单"""
        if order.side == OrderSide.BUY:
            # 检查资金是否充足
            if order.order_type == OrderType.MARKET:
                if order.symbol in self.broker.market_data:
                    required_cash = (order.quantity * 
                                   self.broker.market_data[order.symbol]['price'] * 
                                   (1 + self.broker.commission_rate + self.broker.slippage_rate))
                    return self.cash >= required_cash
            elif order.order_type == OrderType.LIMIT and order.price:
                required_cash = (order.quantity * order.price * 
                               (1 + self.broker.commission_rate))
                return self.cash >= required_cash
        
        elif order.side == OrderSide.SELL:
            # 检查持仓是否充足
            current_position = self.positions.get(order.symbol, {}).get('quantity', 0)
            return current_position >= order.quantity
        
        return True
    
    def _process_fill(self, order: Order):
        """处理成交"""
        symbol = order.symbol
        
        # 计算手续费
        trade_value = order.filled_quantity * order.avg_fill_price
        commission = trade_value * self.broker.commission_rate
        
        if order.side == OrderSide.BUY:
            # 买入
            self.cash -= (trade_value + commission)
            
            if symbol in self.positions:
                # 更新持仓
                old_quantity = self.positions[symbol]['quantity']
                old_avg_price = self.positions[symbol]['avg_price']
                
                new_quantity = old_quantity + order.filled_quantity
                new_avg_price = ((old_quantity * old_avg_price + 
                                trade_value) / new_quantity)
                
                self.positions[symbol] = {
                    'quantity': new_quantity,
                    'avg_price': new_avg_price
                }
            else:
                # 新建持仓
                self.positions[symbol] = {
                    'quantity': order.filled_quantity,
                    'avg_price': order.avg_fill_price
                }
        
        elif order.side == OrderSide.SELL:
            # 卖出
            self.cash += (trade_value - commission)
            
            if symbol in self.positions:
                self.positions[symbol]['quantity'] -= order.filled_quantity
                
                # 如果持仓为0，删除记录
                if self.positions[symbol]['quantity'] <= 0:
                    del self.positions[symbol]
        
        # 记录交易
        self.trade_history.append({
            'timestamp': datetime.now(),
            'order_id': order.order_id,
            'symbol': symbol,
            'side': order.side.value,
            'quantity': order.filled_quantity,
            'price': order.avg_fill_price,
            'commission': commission,
            'cash_after': self.cash
        })
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        if order_id in self.orders:
            order = self.orders[order_id]
            return self.broker.cancel_order(order)
        return False
    
    def get_order_status(self, order_id: str) -> Optional[Dict]:
        """获取订单状态"""
        if order_id in self.orders:
            return self.orders[order_id].to_dict()
        return None
    
    def get_positions(self) -> Dict:
        """获取当前持仓"""
        return self.positions.copy()
    
    def get_portfolio_value(self) -> float:
        """获取组合总价值"""
        total_value = self.cash
        
        for symbol, position in self.positions.items():
            if symbol in self.broker.market_data:
                market_price = self.broker.market_data[symbol]['price']
                total_value += position['quantity'] * market_price
        
        return total_value
    
    def rebalance_portfolio(self, target_weights: Dict[str, float],
                          total_value: float = None) -> List[str]:
        """组合再平衡"""
        if total_value is None:
            total_value = self.get_portfolio_value()
        
        order_ids = []
        
        # 计算目标持仓
        target_positions = {}
        for symbol, weight in target_weights.items():
            if symbol in self.broker.market_data:
                target_value = total_value * weight
                current_price = self.broker.market_data[symbol]['price']
                target_quantity = int(target_value / current_price)
                target_positions[symbol] = target_quantity
        
        # 计算需要调整的持仓
        all_symbols = set(list(self.positions.keys()) + list(target_positions.keys()))
        
        for symbol in all_symbols:
            current_quantity = self.positions.get(symbol, {}).get('quantity', 0)
            target_quantity = target_positions.get(symbol, 0)
            
            if current_quantity != target_quantity:
                trade_quantity = target_quantity - current_quantity
                
                if trade_quantity > 0:
                    # 买入
                    order_id = self.submit_order(
                        symbol=symbol,
                        side=OrderSide.BUY,
                        quantity=trade_quantity,
                        order_type=OrderType.MARKET
                    )
                    order_ids.append(order_id)
                
                elif trade_quantity < 0:
                    # 卖出
                    order_id = self.submit_order(
                        symbol=symbol,
                        side=OrderSide.SELL,
                        quantity=abs(trade_quantity),
                        order_type=OrderType.MARKET
                    )
                    order_ids.append(order_id)
        
        return order_ids
    
    def get_trade_summary(self) -> pd.DataFrame:
        """获取交易汇总"""
        if not self.trade_history:
            return pd.DataFrame()
        
        return pd.DataFrame(self.trade_history)

# 使用示例
def example_trading():
    """交易引擎示例"""
    # 创建模拟券商和交易引擎
    broker = MockBroker(commission_rate=0.0003, slippage_rate=0.0001)
    engine = TradingEngine(broker)
    
    # 更新市场数据
    broker.update_market_data("000001.SZ", 15.50)
    broker.update_market_data("000002.SZ", 25.80)
    
    print("交易引擎初始化完成")
    print(f"初始现金: {engine.cash:,.2f}")
    print(f"手续费率: {broker.commission_rate:.4f}")
    print(f"滑点率: {broker.slippage_rate:.4f}")
    
    # 提交测试订单
    order_id = engine.submit_order(
        symbol="000001.SZ",
        side=OrderSide.BUY,
        quantity=1000,
        order_type=OrderType.MARKET
    )
    
    print(f"\n提交订单: {order_id}")
    print(f"订单状态: {engine.get_order_status(order_id)}")
    print(f"当前持仓: {engine.get_positions()}")
    print(f"组合价值: {engine.get_portfolio_value():,.2f}")
    
    return engine

if __name__ == "__main__":
    engine = example_trading()
