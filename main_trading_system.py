# -*- coding: utf-8 -*-
"""
主交易系统
整合策略引擎、风险管理、回测引擎和交易执行
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from strategy_engine import StrategyManager, FactorMomentumStrategy, MeanReversionStrategy
from backtest_engine import BacktestEngine
from risk_manager import RiskManager
from trading_engine import TradingEngine, MockBroker, OrderSide, OrderType

class MainTradingSystem:
    """主交易系统"""
    
    def __init__(self, initial_capital: float = 1000000):
        """
        初始化交易系统
        
        Parameters:
        -----------
        initial_capital : float
            初始资金
        """
        self.initial_capital = initial_capital
        
        # 初始化各个模块
        self.strategy_manager = StrategyManager()
        self.risk_manager = RiskManager()
        self.backtest_engine = BacktestEngine(initial_capital=initial_capital)
        
        # 交易引擎（实盘时替换为真实券商接口）
        self.broker = MockBroker()
        self.trading_engine = TradingEngine(self.broker)
        
        # 数据存储
        self.factor_data = pd.DataFrame()
        self.factor_returns = pd.DataFrame()
        self.price_data = pd.DataFrame()
        self.factor_covariance = pd.DataFrame()
        self.specific_risk = pd.DataFrame()
        
        # 运行状态
        self.is_running = False
        self.current_positions = pd.DataFrame()
        self.performance_history = []
        
        print("交易系统初始化完成")
        self._setup_default_strategies()
    
    def _setup_default_strategies(self):
        """设置默认策略"""
        # 添加因子动量策略
        momentum_strategy = FactorMomentumStrategy(lookback_period=20)
        self.strategy_manager.add_strategy(momentum_strategy)
        
        # 添加均值回归策略
        mean_reversion_strategy = MeanReversionStrategy(lookback_period=10)
        self.strategy_manager.add_strategy(mean_reversion_strategy)
        
        print(f"已加载策略: {list(self.strategy_manager.strategies.keys())}")
    
    def load_data(self, factor_data_path: str = None, 
                  price_data_path: str = None,
                  factor_returns_path: str = None):
        """
        加载数据
        
        Parameters:
        -----------
        factor_data_path : str
            因子数据文件路径
        price_data_path : str
            价格数据文件路径
        factor_returns_path : str
            因子收益数据文件路径
        """
        try:
            if factor_data_path:
                self.factor_data = pd.read_csv(factor_data_path)
                print(f"已加载因子数据: {len(self.factor_data)} 条记录")
            
            if price_data_path:
                self.price_data = pd.read_csv(price_data_path)
                print(f"已加载价格数据: {len(self.price_data)} 条记录")
            
            if factor_returns_path:
                self.factor_returns = pd.read_csv(factor_returns_path, index_col=0)
                print(f"已加载因子收益数据: {self.factor_returns.shape}")
                
        except Exception as e:
            print(f"数据加载失败: {e}")
    
    def run_backtest(self, strategy_name: str, 
                    start_date: str = None, 
                    end_date: str = None) -> dict:
        """
        运行策略回测
        
        Parameters:
        -----------
        strategy_name : str
            策略名称
        start_date : str
            回测开始日期
        end_date : str
            回测结束日期
        
        Returns:
        --------
        dict: 回测结果
        """
        if self.factor_data.empty or self.factor_returns.empty:
            raise ValueError("请先加载因子数据和因子收益数据")
        
        print(f"\n开始回测策略: {strategy_name}")
        
        # 运行策略生成权重
        strategy_result = self.strategy_manager.run_strategy(
            strategy_name, self.factor_data, self.factor_returns)
        
        weights = strategy_result['weights']
        
        # 风险检查
        print("进行风险检查...")
        risk_violations = []
        
        for date in weights['datetime'].unique():
            date_weights = weights[weights['datetime'] == date]
            risk_check = self.risk_manager.check_position_limits(date_weights)
            
            if not risk_check['passed']:
                risk_violations.extend(risk_check['violations'])
                # 调整权重
                weights.loc[weights['datetime'] == date] = \
                    self.risk_manager.adjust_weights_for_risk(date_weights, risk_check['violations'])
        
        if risk_violations:
            print(f"发现 {len(risk_violations)} 个风险违规，已自动调整")
        
        # 运行回测
        print("运行回测...")
        backtest_result = self.backtest_engine.run_backtest(
            weights, self.price_data, start_date, end_date)
        
        # 生成报告
        performance_metrics = self.backtest_engine.performance_metrics
        
        print(f"\n回测完成！")
        print(f"总收益率: {performance_metrics['total_return']:.2%}")
        print(f"年化收益率: {performance_metrics['annual_return']:.2%}")
        print(f"年化波动率: {performance_metrics['volatility']:.2%}")
        print(f"夏普比率: {performance_metrics['sharpe_ratio']:.3f}")
        print(f"最大回撤: {performance_metrics['max_drawdown']:.2%}")
        
        return {
            'strategy_result': strategy_result,
            'backtest_result': backtest_result,
            'performance_metrics': performance_metrics,
            'risk_violations': risk_violations
        }
    
    def start_live_trading(self, strategy_name: str):
        """
        开始实盘交易
        
        Parameters:
        -----------
        strategy_name : str
            使用的策略名称
        """
        if strategy_name not in self.strategy_manager.strategies:
            raise ValueError(f"策略 {strategy_name} 不存在")
        
        self.is_running = True
        print(f"\n开始实盘交易，使用策略: {strategy_name}")
        print("注意：这是模拟交易，实盘交易需要连接真实券商接口")
        
        # 模拟实盘交易循环
        self._trading_loop(strategy_name)
    
    def _trading_loop(self, strategy_name: str):
        """交易主循环"""
        while self.is_running:
            try:
                current_time = datetime.now()
                print(f"\n[{current_time.strftime('%Y-%m-%d %H:%M:%S')}] 执行交易检查...")
                
                # 获取最新数据（实盘中应该从数据源获取）
                latest_date = self.factor_data['datetime'].max()
                latest_factor_data = self.factor_data[self.factor_data['datetime'] == latest_date]
                
                if len(latest_factor_data) == 0:
                    print("没有最新因子数据，跳过本次交易")
                    break
                
                # 生成交易信号
                strategy_result = self.strategy_manager.run_strategy(
                    strategy_name, latest_factor_data, self.factor_returns)
                
                latest_weights = strategy_result['weights']
                latest_weights = latest_weights[latest_weights['datetime'] == latest_date]
                
                # 风险检查
                risk_check = self.risk_manager.check_position_limits(latest_weights)
                
                if not risk_check['passed']:
                    print(f"风险检查未通过: {len(risk_check['violations'])} 个违规")
                    latest_weights = self.risk_manager.adjust_weights_for_risk(
                        latest_weights, risk_check['violations'])
                    print("已调整权重以符合风险限制")
                
                # 更新市场数据（模拟）
                self._update_market_data(latest_factor_data)
                
                # 执行交易
                target_weights = {}
                for _, row in latest_weights.iterrows():
                    if abs(row['weight']) > 0.001:  # 权重阈值
                        target_weights[row['code']] = row['weight']
                
                if target_weights:
                    print(f"执行再平衡，目标权重数量: {len(target_weights)}")
                    order_ids = self.trading_engine.rebalance_portfolio(target_weights)
                    print(f"提交订单数量: {len(order_ids)}")
                    
                    # 记录当前持仓
                    current_positions = self.trading_engine.get_positions()
                    portfolio_value = self.trading_engine.get_portfolio_value()
                    
                    self.performance_history.append({
                        'timestamp': current_time,
                        'portfolio_value': portfolio_value,
                        'positions_count': len(current_positions),
                        'cash': self.trading_engine.cash
                    })
                    
                    print(f"组合价值: {portfolio_value:,.2f}")
                    print(f"持仓数量: {len(current_positions)}")
                    print(f"现金余额: {self.trading_engine.cash:,.2f}")
                
                # 实盘中这里应该等待下一个交易时点
                break  # 演示模式只运行一次
                
            except Exception as e:
                print(f"交易执行出错: {e}")
                break
    
    def _update_market_data(self, factor_data: pd.DataFrame):
        """更新市场数据（模拟）"""
        # 模拟价格数据
        for _, row in factor_data.iterrows():
            # 使用随机价格模拟（实盘中应该从行情接口获取）
            simulated_price = 10 + np.random.normal(0, 2)
            simulated_price = max(simulated_price, 1.0)  # 确保价格为正
            self.broker.update_market_data(row['code'], simulated_price)
    
    def stop_trading(self):
        """停止交易"""
        self.is_running = False
        print("交易已停止")
    
    def get_performance_summary(self) -> pd.DataFrame:
        """获取性能摘要"""
        if not self.performance_history:
            return pd.DataFrame()
        
        df = pd.DataFrame(self.performance_history)
        
        if len(df) > 1:
            df['returns'] = df['portfolio_value'].pct_change()
            
            summary = {
                '总收益率': (df['portfolio_value'].iloc[-1] / df['portfolio_value'].iloc[0] - 1),
                '当前组合价值': df['portfolio_value'].iloc[-1],
                '现金余额': df['cash'].iloc[-1],
                '平均持仓数量': df['positions_count'].mean(),
                '收益波动率': df['returns'].std() if len(df) > 2 else 0
            }
            
            return pd.DataFrame([summary]).T
        
        return df
    
    def generate_report(self) -> dict:
        """生成综合报告"""
        report = {
            'system_status': {
                'is_running': self.is_running,
                'strategies_loaded': list(self.strategy_manager.strategies.keys()),
                'data_loaded': not self.factor_data.empty,
                'last_update': datetime.now()
            },
            'performance_summary': self.get_performance_summary(),
            'current_positions': self.trading_engine.get_positions(),
            'portfolio_value': self.trading_engine.get_portfolio_value(),
            'trade_history': self.trading_engine.get_trade_summary()
        }
        
        return report

# 使用示例
def example_main_system():
    """主系统使用示例"""
    # 创建主交易系统
    system = MainTradingSystem(initial_capital=1000000)
    
    print("\n=== 交易系统演示 ===")
    
    # 生成模拟数据进行演示
    print("\n1. 生成模拟数据...")
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
    
    # 模拟因子数据
    factor_data = []
    for date in dates[:100]:  # 只用前100天演示
        for stock in stocks:
            factor_data.append({
                'datetime': date.strftime('%Y-%m-%d'),
                'code': stock,
                'Beta': np.random.normal(1, 0.3),
                'Momentum': np.random.normal(0, 1),
                'Size': np.random.normal(0, 1),
                'RV': np.random.normal(0, 1),
                'NLS': np.random.normal(0, 1),
                'BTP': np.random.normal(0, 1),
                'Liquidity': np.random.normal(0, 1),
                'EY': np.random.normal(0, 1),
                'Growth': np.random.normal(0, 1),
                'Leverage': np.random.normal(0, 1)
            })
    
    system.factor_data = pd.DataFrame(factor_data)
    
    # 模拟因子收益数据
    factor_returns = pd.DataFrame(
        np.random.normal(0, 0.01, (100, 10)),
        columns=['Beta', 'Momentum', 'Size', 'RV', 'NLS', 'BTP', 'Liquidity', 'EY', 'Growth', 'Leverage'],
        index=pd.date_range('2023-01-01', periods=100)
    )
    system.factor_returns = factor_returns
    
    # 模拟价格数据
    price_data = []
    for date in dates[:100]:
        for stock in stocks:
            price_data.append({
                'datetime': date.strftime('%Y-%m-%d'),
                'code': stock,
                'close': 10 + np.random.normal(0, 2),
                'pct_chg': np.random.normal(0, 0.02)
            })
    
    system.price_data = pd.DataFrame(price_data)
    
    print("模拟数据生成完成")
    
    # 运行回测
    print("\n2. 运行策略回测...")
    backtest_result = system.run_backtest('FactorMomentum', '2023-01-01', '2023-04-10')
    
    # 模拟实盘交易
    print("\n3. 模拟实盘交易...")
    system.start_live_trading('FactorMomentum')
    
    # 生成报告
    print("\n4. 生成系统报告...")
    report = system.generate_report()
    
    print(f"\n=== 系统状态 ===")
    print(f"运行状态: {report['system_status']['is_running']}")
    print(f"已加载策略: {report['system_status']['strategies_loaded']}")
    print(f"组合价值: {report['portfolio_value']:,.2f}")
    print(f"持仓数量: {len(report['current_positions'])}")
    
    return system

if __name__ == "__main__":
    system = example_main_system()
