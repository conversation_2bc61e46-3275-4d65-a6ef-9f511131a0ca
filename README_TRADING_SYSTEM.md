# Barra风险模型量化交易系统

## 项目概述

本项目基于原有的Barra CNE5中国股票风险模型，扩展为完整的量化交易系统。系统集成了策略开发、风险管理、回测引擎和交易执行等核心功能。

## 系统架构

```
原有Barra风险模型
├── data.py              # 数据获取
├── style_factor.py      # 风格因子构建  
├── factor_exposure.py   # 因子暴露度计算
├── regression.py        # 因子收益率计算
├── utility.py          # 工具函数
└── UI模块              # 图形界面

新增交易系统模块
├── strategy_engine.py   # 策略引擎
├── backtest_engine.py   # 回测引擎
├── risk_manager.py      # 风险管理
├── trading_engine.py    # 交易执行
└── main_trading_system.py # 主控制系统
```

## 核心功能

### 1. 策略开发模块 (strategy_engine.py)
- **因子动量策略**: 基于因子收益动量的多空策略
- **均值回归策略**: 基于因子暴露度极值的反向策略
- **策略管理器**: 统一管理多个策略的运行

### 2. 回测引擎 (backtest_engine.py)
- **完整回测流程**: 支持策略权重到收益的完整回测
- **交易成本模拟**: 包含手续费和滑点成本
- **性能指标计算**: 收益率、夏普比率、最大回撤等
- **可视化分析**: 净值曲线、回撤图表等

### 3. 风险管理 (risk_manager.py)
- **持仓限制检查**: 单股票、行业权重限制
- **杠杆率控制**: 总杠杆率监控
- **换手率限制**: 控制交易频率
- **VaR风险计算**: 基于因子模型的风险预测
- **自动权重调整**: 违规时自动调整权重

### 4. 交易执行 (trading_engine.py)
- **订单管理系统**: 支持市价单、限价单等
- **模拟券商接口**: 可扩展为真实券商API
- **组合再平衡**: 自动计算并执行调仓
- **交易记录**: 完整的交易历史记录

### 5. 主控制系统 (main_trading_system.py)
- **系统集成**: 整合所有模块的主控制器
- **数据管理**: 统一的数据加载和管理
- **实盘交易**: 支持实时策略执行
- **综合报告**: 生成完整的系统运行报告

## 安装和配置

### 环境要求
```bash
Python >= 3.7
pandas >= 1.3.0
numpy >= 1.20.0
matplotlib >= 3.3.0
seaborn >= 0.11.0
scikit-learn >= 0.24.0
statsmodels >= 0.12.0
scipy >= 1.7.0
PyQt5 >= 5.15.0 (原UI模块)
WindPy (万得数据接口，可选)
```

### 安装依赖
```bash
pip install pandas numpy matplotlib seaborn scikit-learn statsmodels scipy
```

## 使用方法

### 1. 基础使用 - 策略回测

```python
from main_trading_system import MainTradingSystem

# 创建交易系统
system = MainTradingSystem(initial_capital=1000000)

# 加载数据
system.load_data(
    factor_data_path='FactorExposure.csv',
    price_data_path='stock_prices.csv', 
    factor_returns_path='FactorReturn.csv'
)

# 运行回测
result = system.run_backtest(
    strategy_name='FactorMomentum',
    start_date='2023-01-01',
    end_date='2023-12-31'
)

# 查看结果
print(f"年化收益率: {result['performance_metrics']['annual_return']:.2%}")
print(f"夏普比率: {result['performance_metrics']['sharpe_ratio']:.3f}")
```

### 2. 高级使用 - 自定义策略

```python
from strategy_engine import BaseStrategy
import pandas as pd
import numpy as np

class CustomStrategy(BaseStrategy):
    def __init__(self):
        super().__init__("CustomStrategy")
    
    def generate_signals(self, factor_data, factor_returns):
        # 实现自定义信号生成逻辑
        signals = pd.DataFrame()
        # ... 策略逻辑 ...
        return signals
    
    def calculate_weights(self, signals, risk_model):
        # 实现自定义权重计算逻辑
        weights = pd.DataFrame()
        # ... 权重计算 ...
        return weights

# 添加到系统
custom_strategy = CustomStrategy()
system.strategy_manager.add_strategy(custom_strategy)
```

### 3. 风险管理配置

```python
from risk_manager import RiskManager

# 创建风险管理器
risk_manager = RiskManager(
    max_position_weight=0.05,    # 单股票最大5%
    max_sector_weight=0.3,       # 单行业最大30%
    max_leverage=1.0,            # 最大杠杆1倍
    max_turnover=2.0,            # 最大换手率200%
    max_drawdown_limit=0.1       # 最大回撤10%
)

# 替换系统默认风险管理器
system.risk_manager = risk_manager
```

### 4. 实盘交易模拟

```python
# 开始实盘交易（模拟模式）
system.start_live_trading('FactorMomentum')

# 查看实时状态
report = system.generate_report()
print(f"组合价值: {report['portfolio_value']:,.2f}")
print(f"持仓数量: {len(report['current_positions'])}")

# 停止交易
system.stop_trading()
```

## 数据格式要求

### 因子暴露度数据 (FactorExposure.csv)
```csv
datetime,code,Beta,Momentum,Size,RV,NLS,BTP,Liquidity,EY,Growth,Leverage,return,weight
2023-01-01,000001.SZ,1.2,0.5,-0.3,0.8,0.1,0.4,-0.2,0.6,0.3,0.7,0.015,0.02
```

### 价格数据 (stock_prices.csv)  
```csv
datetime,code,close,pct_chg
2023-01-01,000001.SZ,15.50,0.015
```

### 因子收益数据 (FactorReturn.csv)
```csv
date,Beta,Momentum,Size,RV,NLS,BTP,Liquidity,EY,Growth,Leverage
2023-01-01,0.002,-0.001,0.003,0.001,0.000,0.002,-0.001,0.001,0.002,0.000
```

## 性能指标说明

### 回测指标
- **总收益率**: 整个回测期间的累计收益
- **年化收益率**: 按年化计算的收益率
- **年化波动率**: 收益率的年化标准差
- **夏普比率**: 超额收益与波动率的比值
- **最大回撤**: 净值从峰值到谷值的最大跌幅

### 风险指标
- **VaR**: 在给定置信水平下的最大可能损失
- **因子风险**: 来自因子暴露的风险贡献
- **特异性风险**: 个股特有的风险贡献
- **杠杆率**: 总持仓权重绝对值之和

## 扩展开发

### 1. 接入真实券商API
```python
# 替换MockBroker为真实券商接口
from real_broker_api import RealBroker

real_broker = RealBroker(api_key='your_api_key')
system.trading_engine = TradingEngine(real_broker)
```

### 2. 添加新的风格因子
```python
# 在style_factor.py中添加新因子
def NEW_FACTOR(self):
    # 实现新因子计算逻辑
    return new_factor_values
```

### 3. 集成机器学习模型
```python
from sklearn.ensemble import RandomForestRegressor

class MLStrategy(BaseStrategy):
    def __init__(self):
        super().__init__("MLStrategy")
        self.model = RandomForestRegressor()
    
    def generate_signals(self, factor_data, factor_returns):
        # 使用机器学习模型生成信号
        # ...
```

## 注意事项

1. **数据质量**: 确保输入数据的完整性和准确性
2. **风险控制**: 实盘交易前充分测试风险管理规则
3. **交易成本**: 根据实际券商费率调整成本参数
4. **市场环境**: 策略在不同市场环境下的表现可能差异很大
5. **合规要求**: 确保策略符合相关法规要求

## 技术支持

如有问题或建议，请参考：
1. 代码注释和文档字符串
2. 示例代码和测试用例
3. 相关学术论文和技术文档

## 免责声明

本系统仅供学习和研究使用，不构成投资建议。实际投资决策请谨慎考虑风险，并咨询专业投资顾问。
