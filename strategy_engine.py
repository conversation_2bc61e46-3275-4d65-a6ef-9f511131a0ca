# -*- coding: utf-8 -*-
"""
策略引擎模块
基于Barra风险模型的量化交易策略
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.positions = {}  # 持仓信息
        self.signals = pd.DataFrame()  # 交易信号
        
    @abstractmethod
    def generate_signals(self, factor_data: pd.DataFrame, 
                        factor_returns: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def calculate_weights(self, signals: pd.DataFrame, 
                         risk_model: Dict) -> pd.DataFrame:
        """计算权重"""
        pass

class FactorMomentumStrategy(BaseStrategy):
    """因子动量策略"""
    
    def __init__(self, lookback_period: int = 20, 
                 rebalance_freq: str = 'M'):
        super().__init__("FactorMomentum")
        self.lookback_period = lookback_period
        self.rebalance_freq = rebalance_freq
        
    def generate_signals(self, factor_data: pd.DataFrame, 
                        factor_returns: pd.DataFrame) -> pd.DataFrame:
        """
        基于因子收益动量生成信号
        """
        signals = pd.DataFrame()
        
        # 计算因子收益动量
        factor_momentum = factor_returns.rolling(
            window=self.lookback_period).mean()
        
        # 选择动量最强的因子
        top_factors = factor_momentum.rank(axis=1, ascending=False) <= 3
        
        # 生成股票信号
        for date in factor_data['datetime'].unique():
            date_data = factor_data[factor_data['datetime'] == date]
            
            if date in factor_momentum.index:
                # 获取当日最强因子
                strong_factors = top_factors.loc[date]
                strong_factor_names = strong_factors[strong_factors].index.tolist()
                
                # 计算股票在强势因子上的暴露度
                stock_scores = date_data[strong_factor_names].sum(axis=1)
                
                # 生成信号：做多高暴露度股票，做空低暴露度股票
                signals_date = pd.DataFrame({
                    'datetime': date,
                    'code': date_data['code'],
                    'signal': np.where(stock_scores > stock_scores.quantile(0.8), 1,
                             np.where(stock_scores < stock_scores.quantile(0.2), -1, 0))
                })
                
                signals = pd.concat([signals, signals_date], ignore_index=True)
        
        return signals
    
    def calculate_weights(self, signals: pd.DataFrame, 
                         risk_model: Dict) -> pd.DataFrame:
        """
        基于风险模型优化权重
        """
        weights = pd.DataFrame()
        
        for date in signals['datetime'].unique():
            date_signals = signals[signals['datetime'] == date]
            
            # 简单等权重分配（可扩展为风险平价或均值方差优化）
            long_stocks = date_signals[date_signals['signal'] == 1]
            short_stocks = date_signals[date_signals['signal'] == -1]
            
            date_weights = pd.DataFrame({
                'datetime': date,
                'code': date_signals['code'],
                'weight': 0.0
            })
            
            if len(long_stocks) > 0:
                long_weight = 0.5 / len(long_stocks)
                date_weights.loc[date_weights['code'].isin(long_stocks['code']), 'weight'] = long_weight
            
            if len(short_stocks) > 0:
                short_weight = -0.5 / len(short_stocks)
                date_weights.loc[date_weights['code'].isin(short_stocks['code']), 'weight'] = short_weight
            
            weights = pd.concat([weights, date_weights], ignore_index=True)
        
        return weights

class MeanReversionStrategy(BaseStrategy):
    """均值回归策略"""
    
    def __init__(self, lookback_period: int = 10, 
                 zscore_threshold: float = 2.0):
        super().__init__("MeanReversion")
        self.lookback_period = lookback_period
        self.zscore_threshold = zscore_threshold
        
    def generate_signals(self, factor_data: pd.DataFrame, 
                        factor_returns: pd.DataFrame) -> pd.DataFrame:
        """
        基于因子暴露度均值回归生成信号
        """
        signals = pd.DataFrame()
        
        # 计算因子暴露度的Z-score
        factor_cols = ['Beta', 'Momentum', 'Size', 'RV', 'NLS', 
                      'BTP', 'Liquidity', 'EY', 'Growth', 'Leverage']
        
        for date in factor_data['datetime'].unique():
            date_data = factor_data[factor_data['datetime'] == date].copy()
            
            # 计算Z-score
            for factor in factor_cols:
                if factor in date_data.columns:
                    mean_val = date_data[factor].mean()
                    std_val = date_data[factor].std()
                    date_data[f'{factor}_zscore'] = (date_data[factor] - mean_val) / std_val
            
            # 生成信号：极端值反向操作
            zscore_cols = [f'{factor}_zscore' for factor in factor_cols if f'{factor}_zscore' in date_data.columns]
            avg_zscore = date_data[zscore_cols].mean(axis=1)
            
            signals_date = pd.DataFrame({
                'datetime': date,
                'code': date_data['code'],
                'signal': np.where(avg_zscore > self.zscore_threshold, -1,
                         np.where(avg_zscore < -self.zscore_threshold, 1, 0))
            })
            
            signals = pd.concat([signals, signals_date], ignore_index=True)
        
        return signals
    
    def calculate_weights(self, signals: pd.DataFrame, 
                         risk_model: Dict) -> pd.DataFrame:
        """计算权重"""
        return FactorMomentumStrategy().calculate_weights(signals, risk_model)

class StrategyManager:
    """策略管理器"""
    
    def __init__(self):
        self.strategies = {}
        self.results = {}
        
    def add_strategy(self, strategy: BaseStrategy):
        """添加策略"""
        self.strategies[strategy.name] = strategy
        
    def run_strategy(self, strategy_name: str, 
                    factor_data: pd.DataFrame,
                    factor_returns: pd.DataFrame,
                    risk_model: Dict = None) -> Dict:
        """运行策略"""
        if strategy_name not in self.strategies:
            raise ValueError(f"Strategy {strategy_name} not found")
        
        strategy = self.strategies[strategy_name]
        
        # 生成信号
        signals = strategy.generate_signals(factor_data, factor_returns)
        
        # 计算权重
        weights = strategy.calculate_weights(signals, risk_model or {})
        
        # 保存结果
        self.results[strategy_name] = {
            'signals': signals,
            'weights': weights,
            'strategy': strategy
        }
        
        return self.results[strategy_name]
    
    def get_portfolio_weights(self, strategy_name: str, date: str) -> pd.DataFrame:
        """获取指定日期的组合权重"""
        if strategy_name not in self.results:
            raise ValueError(f"No results for strategy {strategy_name}")
        
        weights = self.results[strategy_name]['weights']
        return weights[weights['datetime'] == date]

# 使用示例
def example_usage():
    """使用示例"""
    # 创建策略管理器
    manager = StrategyManager()
    
    # 添加策略
    momentum_strategy = FactorMomentumStrategy(lookback_period=20)
    mean_reversion_strategy = MeanReversionStrategy(lookback_period=10)
    
    manager.add_strategy(momentum_strategy)
    manager.add_strategy(mean_reversion_strategy)
    
    print("策略引擎初始化完成")
    print(f"可用策略: {list(manager.strategies.keys())}")
    
    return manager

if __name__ == "__main__":
    manager = example_usage()
