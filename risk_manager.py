# -*- coding: utf-8 -*-
"""
风险管理模块
提供实时风险监控和控制功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class RiskManager:
    """风险管理器"""
    
    def __init__(self, 
                 max_position_weight: float = 0.05,
                 max_sector_weight: float = 0.3,
                 max_leverage: float = 1.0,
                 max_turnover: float = 2.0,
                 var_confidence: float = 0.05,
                 max_drawdown_limit: float = 0.1):
        """
        初始化风险管理器
        
        Parameters:
        -----------
        max_position_weight : float
            单个股票最大权重
        max_sector_weight : float  
            单个行业最大权重
        max_leverage : float
            最大杠杆率
        max_turnover : float
            最大换手率
        var_confidence : float
            VaR置信水平
        max_drawdown_limit : float
            最大回撤限制
        """
        self.max_position_weight = max_position_weight
        self.max_sector_weight = max_sector_weight
        self.max_leverage = max_leverage
        self.max_turnover = max_turnover
        self.var_confidence = var_confidence
        self.max_drawdown_limit = max_drawdown_limit
        
        # 风险监控记录
        self.risk_alerts = []
        self.risk_metrics_history = pd.DataFrame()
        
    def check_position_limits(self, weights: pd.DataFrame, 
                            industry_mapping: Dict = None) -> Dict:
        """
        检查持仓限制
        
        Parameters:
        -----------
        weights : pd.DataFrame
            权重数据，包含columns: ['code', 'weight']
        industry_mapping : Dict
            股票行业映射 {code: industry}
        
        Returns:
        --------
        Dict: 风险检查结果
        """
        violations = []
        
        # 检查单股票权重限制
        max_weight = weights['weight'].abs().max()
        if max_weight > self.max_position_weight:
            max_stock = weights.loc[weights['weight'].abs().idxmax(), 'code']
            violations.append({
                'type': 'position_limit',
                'message': f'股票 {max_stock} 权重 {max_weight:.3f} 超过限制 {self.max_position_weight:.3f}',
                'severity': 'high',
                'value': max_weight,
                'limit': self.max_position_weight
            })
        
        # 检查行业权重限制
        if industry_mapping:
            weights_with_industry = weights.copy()
            weights_with_industry['industry'] = weights_with_industry['code'].map(industry_mapping)
            
            industry_weights = weights_with_industry.groupby('industry')['weight'].sum().abs()
            max_industry_weight = industry_weights.max()
            
            if max_industry_weight > self.max_sector_weight:
                max_industry = industry_weights.idxmax()
                violations.append({
                    'type': 'sector_limit',
                    'message': f'行业 {max_industry} 权重 {max_industry_weight:.3f} 超过限制 {self.max_sector_weight:.3f}',
                    'severity': 'medium',
                    'value': max_industry_weight,
                    'limit': self.max_sector_weight
                })
        
        # 检查杠杆率
        total_leverage = weights['weight'].abs().sum()
        if total_leverage > self.max_leverage:
            violations.append({
                'type': 'leverage_limit',
                'message': f'总杠杆率 {total_leverage:.3f} 超过限制 {self.max_leverage:.3f}',
                'severity': 'high',
                'value': total_leverage,
                'limit': self.max_leverage
            })
        
        return {
            'passed': len(violations) == 0,
            'violations': violations,
            'total_leverage': total_leverage,
            'max_position_weight': max_weight
        }
    
    def check_turnover_limit(self, current_weights: pd.DataFrame,
                           target_weights: pd.DataFrame) -> Dict:
        """
        检查换手率限制
        
        Parameters:
        -----------
        current_weights : pd.DataFrame
            当前权重
        target_weights : pd.DataFrame  
            目标权重
        
        Returns:
        --------
        Dict: 换手率检查结果
        """
        # 合并当前权重和目标权重
        merged = pd.merge(current_weights[['code', 'weight']], 
                         target_weights[['code', 'weight']], 
                         on='code', how='outer', suffixes=('_current', '_target'))
        
        merged['weight_current'] = merged['weight_current'].fillna(0)
        merged['weight_target'] = merged['weight_target'].fillna(0)
        
        # 计算换手率
        turnover = (merged['weight_target'] - merged['weight_current']).abs().sum()
        
        violations = []
        if turnover > self.max_turnover:
            violations.append({
                'type': 'turnover_limit',
                'message': f'换手率 {turnover:.3f} 超过限制 {self.max_turnover:.3f}',
                'severity': 'medium',
                'value': turnover,
                'limit': self.max_turnover
            })
        
        return {
            'passed': len(violations) == 0,
            'violations': violations,
            'turnover': turnover
        }
    
    def calculate_portfolio_risk(self, weights: pd.DataFrame,
                               factor_exposure: pd.DataFrame,
                               factor_covariance: pd.DataFrame,
                               specific_risk: pd.DataFrame) -> Dict:
        """
        计算组合风险
        
        Parameters:
        -----------
        weights : pd.DataFrame
            权重数据
        factor_exposure : pd.DataFrame
            因子暴露度矩阵
        factor_covariance : pd.DataFrame
            因子协方差矩阵
        specific_risk : pd.DataFrame
            特异性风险
        
        Returns:
        --------
        Dict: 风险指标
        """
        # 合并权重和因子暴露度
        portfolio_data = pd.merge(weights, factor_exposure, on='code', how='inner')
        
        if len(portfolio_data) == 0:
            return {'error': '无法匹配权重和因子数据'}
        
        # 计算组合因子暴露度
        factor_cols = [col for col in factor_exposure.columns if col not in ['code', 'datetime']]
        portfolio_exposure = {}
        
        for factor in factor_cols:
            if factor in portfolio_data.columns:
                portfolio_exposure[factor] = (portfolio_data['weight'] * 
                                            portfolio_data[factor]).sum()
        
        # 计算因子风险贡献
        factor_risk = 0
        if len(portfolio_exposure) > 0 and not factor_covariance.empty:
            exposure_vector = pd.Series(portfolio_exposure)
            common_factors = exposure_vector.index.intersection(factor_covariance.index)
            
            if len(common_factors) > 0:
                exposure_aligned = exposure_vector[common_factors]
                cov_aligned = factor_covariance.loc[common_factors, common_factors]
                factor_risk = np.sqrt(exposure_aligned.T @ cov_aligned @ exposure_aligned)
        
        # 计算特异性风险贡献
        specific_risk_contrib = 0
        if not specific_risk.empty:
            portfolio_specific = pd.merge(portfolio_data[['code', 'weight']], 
                                        specific_risk, on='code', how='inner')
            if len(portfolio_specific) > 0 and 'specific_risk' in portfolio_specific.columns:
                specific_risk_contrib = np.sqrt((portfolio_specific['weight']**2 * 
                                               portfolio_specific['specific_risk']**2).sum())
        
        # 总风险
        total_risk = np.sqrt(factor_risk**2 + specific_risk_contrib**2)
        
        # 计算VaR
        from scipy.stats import norm
        var_95 = total_risk * norm.ppf(1 - self.var_confidence)
        
        return {
            'total_risk': total_risk,
            'factor_risk': factor_risk,
            'specific_risk': specific_risk_contrib,
            'var_95': var_95,
            'portfolio_exposure': portfolio_exposure,
            'risk_decomposition': {
                'factor_contribution': factor_risk**2 / (total_risk**2) if total_risk > 0 else 0,
                'specific_contribution': specific_risk_contrib**2 / (total_risk**2) if total_risk > 0 else 0
            }
        }
    
    def check_drawdown_limit(self, portfolio_values: pd.DataFrame) -> Dict:
        """
        检查回撤限制
        
        Parameters:
        -----------
        portfolio_values : pd.DataFrame
            组合净值序列，包含columns: ['datetime', 'total_value']
        
        Returns:
        --------
        Dict: 回撤检查结果
        """
        if len(portfolio_values) < 2:
            return {'passed': True, 'violations': [], 'current_drawdown': 0}
        
        # 计算回撤
        cumulative_return = portfolio_values['total_value'] / portfolio_values['total_value'].iloc[0]
        running_max = cumulative_return.expanding().max()
        drawdown = (cumulative_return - running_max) / running_max
        
        current_drawdown = drawdown.iloc[-1]
        max_drawdown = drawdown.min()
        
        violations = []
        if abs(current_drawdown) > self.max_drawdown_limit:
            violations.append({
                'type': 'drawdown_limit',
                'message': f'当前回撤 {abs(current_drawdown):.3f} 超过限制 {self.max_drawdown_limit:.3f}',
                'severity': 'critical',
                'value': abs(current_drawdown),
                'limit': self.max_drawdown_limit
            })
        
        return {
            'passed': len(violations) == 0,
            'violations': violations,
            'current_drawdown': current_drawdown,
            'max_drawdown': max_drawdown
        }
    
    def adjust_weights_for_risk(self, weights: pd.DataFrame,
                              violations: List[Dict]) -> pd.DataFrame:
        """
        根据风险违规调整权重
        
        Parameters:
        -----------
        weights : pd.DataFrame
            原始权重
        violations : List[Dict]
            风险违规列表
        
        Returns:
        --------
        pd.DataFrame: 调整后的权重
        """
        adjusted_weights = weights.copy()
        
        for violation in violations:
            if violation['type'] == 'position_limit':
                # 缩放超限持仓
                max_weight_idx = adjusted_weights['weight'].abs().idxmax()
                scale_factor = self.max_position_weight / abs(adjusted_weights.loc[max_weight_idx, 'weight'])
                adjusted_weights.loc[max_weight_idx, 'weight'] *= scale_factor
                
            elif violation['type'] == 'leverage_limit':
                # 等比例缩放所有权重
                scale_factor = self.max_leverage / adjusted_weights['weight'].abs().sum()
                adjusted_weights['weight'] *= scale_factor
        
        return adjusted_weights
    
    def generate_risk_report(self, weights: pd.DataFrame,
                           factor_exposure: pd.DataFrame = None,
                           factor_covariance: pd.DataFrame = None,
                           specific_risk: pd.DataFrame = None,
                           industry_mapping: Dict = None) -> Dict:
        """
        生成风险报告
        
        Returns:
        --------
        Dict: 完整的风险报告
        """
        report = {
            'timestamp': datetime.now(),
            'position_check': self.check_position_limits(weights, industry_mapping),
            'risk_metrics': {}
        }
        
        # 计算风险指标
        if (factor_exposure is not None and 
            factor_covariance is not None and 
            specific_risk is not None):
            report['risk_metrics'] = self.calculate_portfolio_risk(
                weights, factor_exposure, factor_covariance, specific_risk)
        
        # 汇总风险等级
        all_violations = report['position_check']['violations']
        
        if any(v['severity'] == 'critical' for v in all_violations):
            report['overall_risk_level'] = 'critical'
        elif any(v['severity'] == 'high' for v in all_violations):
            report['overall_risk_level'] = 'high'
        elif any(v['severity'] == 'medium' for v in all_violations):
            report['overall_risk_level'] = 'medium'
        else:
            report['overall_risk_level'] = 'low'
        
        # 保存到历史记录
        self.risk_alerts.extend(all_violations)
        
        return report

# 使用示例
def example_risk_management():
    """风险管理示例"""
    # 创建风险管理器
    risk_manager = RiskManager(
        max_position_weight=0.05,
        max_sector_weight=0.3,
        max_leverage=1.0,
        max_turnover=2.0
    )
    
    print("风险管理器初始化完成")
    print(f"单股票最大权重: {risk_manager.max_position_weight:.1%}")
    print(f"单行业最大权重: {risk_manager.max_sector_weight:.1%}")
    print(f"最大杠杆率: {risk_manager.max_leverage:.1f}")
    print(f"最大换手率: {risk_manager.max_turnover:.1f}")
    
    return risk_manager

if __name__ == "__main__":
    risk_manager = example_risk_management()
